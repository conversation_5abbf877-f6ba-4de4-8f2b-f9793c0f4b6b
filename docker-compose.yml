# version: '3.8'
services:
  db:
    image: postgres:15-alpine
    container_name: suitsync-db
    restart: always
    environment:
      POSTGRES_DB: suitsync_prod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - '5432:5432'
    volumes:
      - suitsync_db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      target: production
    container_name: suitsync-backend
    restart: always
    depends_on:
      db:
        condition: service_healthy
    env_file:
      - ./.env
    environment:
      DATABASE_URL: **************************************/suitsync_prod
      SESSION_SECRET: super_secret_session_key_1234567890
      FRONTEND_URL: http://localhost:3001
      NODE_ENV: production
    ports:
      - '3000:3000'
    volumes:
      - suitsync_sessions:/app/sessions
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  frontend:
    build:
      context: ./frontend
      target: production
    container_name: suitsync-frontend
    restart: always
    env_file:
      - ./frontend/.env.docker
    environment:
      NEXT_PUBLIC_BACKEND_URL: http://backend:3000
      NODE_ENV: production
    ports:
      - '3001:3000'
    depends_on:
      - backend

  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data



volumes:
  suitsync_db_data:
  suitsync_sessions:
  redis-data:

networks:
  suitsync-net:
    driver: bridge 