if(!self.define){let e,s={};const a=(a,c)=>(a=new URL(a+".js",c).href,s[a]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()}).then(()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e}));self.define=(c,n)=>{const t=e||("document"in self?document.currentScript.src:"")||location.href;if(s[t])return;let i={};const r=e=>a(e,t),d={module:{uri:t},exports:i,require:r};s[t]=Promise.all(c.map(e=>d[e]||r(e))).then(e=>(n(...e),i))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/static/Ftjl2m0vszQXVZuPTMM3J/_buildManifest.js",revision:"9ecad94bdafa6261ba76aa527b2e792c"},{url:"/_next/static/Ftjl2m0vszQXVZuPTMM3J/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/1291-8c0c2a20495f5d8e.js",revision:"8c0c2a20495f5d8e"},{url:"/_next/static/chunks/1401-aae4911965fd8997.js",revision:"aae4911965fd8997"},{url:"/_next/static/chunks/1541-5b127dcf1e24cbe1.js",revision:"5b127dcf1e24cbe1"},{url:"/_next/static/chunks/2565-23005ad83d99b8fc.js",revision:"23005ad83d99b8fc"},{url:"/_next/static/chunks/3442.148bb0913d11629a.js",revision:"148bb0913d11629a"},{url:"/_next/static/chunks/3650-1cbeb97792f4c8ba.js",revision:"1cbeb97792f4c8ba"},{url:"/_next/static/chunks/4563-56fe1e4c70f8c928.js",revision:"56fe1e4c70f8c928"},{url:"/_next/static/chunks/4602-6e8a8c69a7d6f28e.js",revision:"6e8a8c69a7d6f28e"},{url:"/_next/static/chunks/5-7d33f20c7def01ca.js",revision:"7d33f20c7def01ca"},{url:"/_next/static/chunks/5085-6130dc1bffa3a9fd.js",revision:"6130dc1bffa3a9fd"},{url:"/_next/static/chunks/555-42663f1533fadb84.js",revision:"42663f1533fadb84"},{url:"/_next/static/chunks/6048-02e84a3b87815ab4.js",revision:"02e84a3b87815ab4"},{url:"/_next/static/chunks/7136-138415ee89cb30cb.js",revision:"138415ee89cb30cb"},{url:"/_next/static/chunks/7587-76335b02a8173a9a.js",revision:"76335b02a8173a9a"},{url:"/_next/static/chunks/82-2bf63cff4c5a1947.js",revision:"2bf63cff4c5a1947"},{url:"/_next/static/chunks/8995-45ca4731c515a92b.js",revision:"45ca4731c515a92b"},{url:"/_next/static/chunks/9714-31a07052b293d16d.js",revision:"31a07052b293d16d"},{url:"/_next/static/chunks/98f61148-2a5b4f506c1660bd.js",revision:"2a5b4f506c1660bd"},{url:"/_next/static/chunks/framework-56eb74ff06128874.js",revision:"56eb74ff06128874"},{url:"/_next/static/chunks/main-8478c91d847d1a4d.js",revision:"8478c91d847d1a4d"},{url:"/_next/static/chunks/pages/UserSettings-a5d762dde90a0a82.js",revision:"a5d762dde90a0a82"},{url:"/_next/static/chunks/pages/_app-866bd44772a823bc.js",revision:"866bd44772a823bc"},{url:"/_next/static/chunks/pages/_error-d2da72dbcb17ea77.js",revision:"d2da72dbcb17ea77"},{url:"/_next/static/chunks/pages/admin-afbf80c6a3b06618.js",revision:"afbf80c6a3b06618"},{url:"/_next/static/chunks/pages/admin/notification-settings-371f152e1ce82da6.js",revision:"371f152e1ce82da6"},{url:"/_next/static/chunks/pages/alt-calendar-479d6bb7bead2221.js",revision:"479d6bb7bead2221"},{url:"/_next/static/chunks/pages/alteration-job/%5Bid%5D-859196164741fd3f.js",revision:"859196164741fd3f"},{url:"/_next/static/chunks/pages/alteration-job/%5Bid%5D-99f248512e7d361f.js",revision:"99f248512e7d361f"},{url:"/_next/static/chunks/pages/alterations-f6531ad891abf04e.js",revision:"f6531ad891abf04e"},{url:"/_next/static/chunks/pages/alterations-scan-abd0b906dbfc9238.js",revision:"abd0b906dbfc9238"},{url:"/_next/static/chunks/pages/appointments-f594492591ff0149.js",revision:"f594492591ff0149"},{url:"/_next/static/chunks/pages/appointments/cancel-810fe5b536a6a3cc.js",revision:"810fe5b536a6a3cc"},{url:"/_next/static/chunks/pages/appointments/cancel-success-1525cfc28b988f5b.js",revision:"1525cfc28b988f5b"},{url:"/_next/static/chunks/pages/appointments/reschedule-f5011a038f87add0.js",revision:"f5011a038f87add0"},{url:"/_next/static/chunks/pages/appointments/reschedule-success-63295195c8793ec9.js",revision:"63295195c8793ec9"},{url:"/_next/static/chunks/pages/checklists-240e5545942c017b.js",revision:"240e5545942c017b"},{url:"/_next/static/chunks/pages/create-alteration-b88dd2a3c27b8ee7.js",revision:"b88dd2a3c27b8ee7"},{url:"/_next/static/chunks/pages/create-appointment-c967efdf240f6a5a.js",revision:"c967efdf240f6a5a"},{url:"/_next/static/chunks/pages/create-party-6bbef77701405dbe.js",revision:"6bbef77701405dbe"},{url:"/_next/static/chunks/pages/customers-9371d940129bf225.js",revision:"9371d940129bf225"},{url:"/_next/static/chunks/pages/customers/%5Bid%5D-75a82f77672ee977.js",revision:"75a82f77672ee977"},{url:"/_next/static/chunks/pages/customers/%5Bid%5D-8444a1938bc5bb64.js",revision:"8444a1938bc5bb64"},{url:"/_next/static/chunks/pages/customers/%5Bid%5D/measurements-89f9d34d696686aa.js",revision:"89f9d34d696686aa"},{url:"/_next/static/chunks/pages/customers/%5Bid%5D/measurements-b5172a343621688a.js",revision:"b5172a343621688a"},{url:"/_next/static/chunks/pages/dashboard-3c18f33c02942098.js",revision:"3c18f33c02942098"},{url:"/_next/static/chunks/pages/index-2dfa77182535f9a8.js",revision:"2dfa77182535f9a8"},{url:"/_next/static/chunks/pages/lightspeed-account-dd4dc785a2cf2a1a.js",revision:"dd4dc785a2cf2a1a"},{url:"/_next/static/chunks/pages/login-b711ce8121e5b33d.js",revision:"b711ce8121e5b33d"},{url:"/_next/static/chunks/pages/logout-0dc453bc9a68864f.js",revision:"0dc453bc9a68864f"},{url:"/_next/static/chunks/pages/monitoring-8d7a71a58d0c4766.js",revision:"8d7a71a58d0c4766"},{url:"/_next/static/chunks/pages/parties-892c2465989fa3d3.js",revision:"892c2465989fa3d3"},{url:"/_next/static/chunks/pages/parties/%5Bid%5D-12de03273819a45d.js",revision:"12de03273819a45d"},{url:"/_next/static/chunks/pages/parties/%5Bid%5D-8c679d4cbdd6965f.js",revision:"8c679d4cbdd6965f"},{url:"/_next/static/chunks/pages/party-dashboard-a7fa995ee47781af.js",revision:"a7fa995ee47781af"},{url:"/_next/static/chunks/pages/profile-69f90663d208cf7a.js",revision:"69f90663d208cf7a"},{url:"/_next/static/chunks/pages/qr-status-update-7cd34bd3b4e94695.js",revision:"7cd34bd3b4e94695"},{url:"/_next/static/chunks/pages/sales-c22976b719f39c08.js",revision:"c22976b719f39c08"},{url:"/_next/static/chunks/pages/sales-workflow-a93a8ac2ab308a90.js",revision:"a93a8ac2ab308a90"},{url:"/_next/static/chunks/pages/settings-14cf3b29c5b62b02.js",revision:"14cf3b29c5b62b02"},{url:"/_next/static/chunks/pages/setup-pin-341a3d52b4f6588a.js",revision:"341a3d52b4f6588a"},{url:"/_next/static/chunks/pages/status-bb97b366312af61b.js",revision:"bb97b366312af61b"},{url:"/_next/static/chunks/pages/tag-591152a572043355.js",revision:"591152a572043355"},{url:"/_next/static/chunks/pages/tailor-schedule-8d4dbb966654ca5a.js",revision:"8d4dbb966654ca5a"},{url:"/_next/static/chunks/pages/tasks-4206fea732962bec.js",revision:"4206fea732962bec"},{url:"/_next/static/chunks/pages/users-6a10a0d360519c37.js",revision:"6a10a0d360519c37"},{url:"/_next/static/chunks/polyfills-78c92fac7aa8fdd8.js",revision:"79330112775102f91e1010318bae2bd3"},{url:"/_next/static/chunks/webpack-126ccb6972ec64a3.js",revision:"126ccb6972ec64a3"},{url:"/_next/static/css/be90309d9ca0ee67.css",revision:"be90309d9ca0ee67"},{url:"/_next/static/css/e22327bc388712a9.css",revision:"e22327bc388712a9"},{url:"/android-chrome-192x192.png",revision:"7ed1099a10e167626cb0cd123f2d521b"},{url:"/android-chrome-512x512.png",revision:"b309f997ca2b344bbc934d17ae6cd905"},{url:"/apple-touch-icon.png",revision:"f6d6dd1277b74a4bfbffa3b47abf0676"},{url:"/favicon-16x16.png",revision:"a1b200ec098567dc3bf4517ad8836d0a"},{url:"/favicon-32x32.png",revision:"45d0435397bc0f502f5313e927293767"},{url:"/favicon.ico",revision:"64b744ab55ebc6824b4ed64423eb01d4"},{url:"/garmenttags.pdf",revision:"c454605392781f20b5a6d04f18b210df"},{url:"/offline.html",revision:"873a68cfc8eac5d320dfdca2daa5f03b"},{url:"/partycard.pdf",revision:"7cb2b9c91708870747ea3816ef890468"},{url:"/riverside-logo-full.jpg",revision:"814488e95fe128c69ccba5ce221994c8"},{url:"/riverside-logo-icon.jpg",revision:"de8fc86e97f382e5fb5aeacf4a91d5dc"},{url:"/site.webmanifest",revision:"57cd1badd53f08c4a4dc04b3237d1a3f"},{url:"/suitsync-logoh.png",revision:"28e83f4dd14cb014e7216e9c98370b9d"},{url:"/suitsync-logov.png",revision:"2d552cc09a40cf7e950440032bc6cc0c"},{url:"/suitsync-logow.png",revision:"97796ce5a7bc000734dc28e8f6b07687"},{url:"/suitsynclogo-only.png",revision:"0c37b9df275b59f1cb3a0a8a6104b754"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:c})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET"),e.registerRoute(/^\/_next\/static\/.*/i,new e.NetworkFirst({cacheName:"next-static-cache",plugins:[]}),"GET"),e.registerRoute(/^\/_next\/image\?url=.*$/i,new e.NetworkFirst({cacheName:"next-image-cache",plugins:[]}),"GET")});
