# Dependency directories
node_modules/
.pnpm-store/

# Build output
.next/
dist/
out/
coverage/

# Logs
*.log

# TypeScript
*.tsbuildinfo

# Environment files
.env
.env.*

# Lockfiles
pnpm-lock.yaml
package-lock.json
yarn.lock

# OS files
.DS_Store
Thumbs.db

# Misc
*.swp
*.swo

# Ignore backend and frontend build/deps explicitly
backend/node_modules/
backend/dist/
backend/.env
backend/pnpm-lock.yaml
frontend/node_modules/
frontend/dist/
frontend/.env
frontend/pnpm-lock.yaml
frontend/.next/
frontend/out/
frontend/.pnpm-store/
