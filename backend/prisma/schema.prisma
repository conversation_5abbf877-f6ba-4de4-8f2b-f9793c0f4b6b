// prisma/schema.prisma

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
  // output   = "../server/node_modules/.prisma/client"
}

model Customer {
  id                Int             @id @default(autoincrement())
  name              String         // Deprecated, for migration only
  first_name        String?
  last_name         String?
  email             String?
  phone             String?
  address           String?
  lightspeedId      String          @unique
  lightspeedVersion BigInt?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  syncedAt          DateTime?
  createdBy         Int?
  measurements      Measurements?
  parties           Party[]
  alterationJobs    AlterationJob[]
  createdByUser     User?           @relation("CustomerCreator", fields: [createdBy], references: [id])
  sales             Sale[]
  individualAppointments Appointment[] @relation("IndividualCustomerAppointments")
  tags              CustomerTag[]     @relation("CustomerTags")
  customFields      CustomerCustomField[]
  groups            CustomerGroup[]   @relation("CustomerGroups")
  notifications     NotificationHistory[] @relation("CustomerNotifications")

  @@index([lightspeedId])
  @@index([email])
  @@index([name])
  @@index([phone])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([syncedAt])
  @@index([createdBy])
  @@index([lightspeedVersion])
}

model Measurements {
  id            Int      @id @default(autoincrement())
  customerId    Int      @unique
  chest         String?
  waistJacket   String?
  hips          String?
  shoulderWidth String?
  sleeveLength  String?
  jacketLength  String?
  overarm       String?
  neck          String?
  trouserWaist  String?
  inseam        String?
  outseam       String?
  height        String?
  weight        String?
  shirtCollar   String?
  shirtSleeve   String?
  fitPreference String?
  outOfTown     Boolean  @default(false)
  notes         String?
  updatedAt     DateTime @updatedAt
  customer      Customer @relation(fields: [customerId], references: [id])
}

model User {
  id                   Int                 @id @default(autoincrement())
  email                String              @unique
  passwordHash         String?
  name                 String
  role                 String
  commissionRate       Float?              @default(0.1)
  appointments         Appointment[]       @relation("TailorAppointments")
  alterationJobs       AlterationJob[]
  auditLogs            AuditLog[]
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  saleAssignments      SaleAssignment[]    @relation("UserSaleAssignments")
  skills               Skill[]
  tailorAbilities      TailorAbility[]     @relation("UserTailorAbilities")
  tailorSchedules      TailorSchedule[]    @relation("UserTailorSchedules")
  assignedJobParts     AlterationJobPart[] @relation("AssignedTailorJobParts")
  lightspeedEmployeeId String?             @unique
  photoUrl             String?
  notificationPrefs    Json?
  createdCustomers     Customer[]          @relation("CustomerCreator")
  createdWeddingSuits  WeddingSuit[]       @relation("WeddingSuitCreator")
  taskLogs             AlterationTaskLog[] @relation("TaskLogUser")
  qrScans              QRScanLog[]         @relation("QRScanUser")
  workflowStepsCompleted AlterationWorkflowStep[] @relation("WorkflowStepUser")
  assignedAlterationTasks AlterationTask[]    @relation("TaskAssignedUser")
  assignmentLogs       AssignmentLog[]
  userSchedules        UserSchedule[]
  userSessions         UserSession[]       @relation("UserSessions")
  parties              Party[]             @relation("PartySalesPerson")
  notifications        NotificationHistory[] @relation("UserNotifications")
  createdTemplates     NotificationTemplate[] @relation("TemplateCreator")
  
  // Checklists & Tasks relations
  createdChecklists    Checklist[]         @relation("ChecklistCreator")
  assignedChecklists   ChecklistAssignment[] @relation("ChecklistAssignee")
  assignedChecklistsBy ChecklistAssignment[] @relation("ChecklistAssigner")
  checklistExecutions  ChecklistExecution[] @relation("ChecklistExecutor")
  assignedTasks        Task[]              @relation("TaskAssignee")
  assignedTasksBy      Task[]              @relation("TaskAssigner")

  // PIN-based user switching
  pinHash              String?
  pinSetAt             DateTime?
  pinAttempts          Int?                @default(0)
  pinLockedUntil       DateTime?
  lastPinUse           DateTime?

  @@index([email])
  @@index([role])
  @@index([lightspeedEmployeeId])
  @@index([createdAt])
  @@index([name])
  @@index([pinHash])
  @@index([pinLockedUntil])
}

model UserSchedule {
  id           Int      @id @default(autoincrement())
  user         User     @relation(fields: [userId], references: [id])
  userId       Int
  weekStart    DateTime? // null = default/recurring schedule, otherwise Monday of week
  days         Json      // Array of 7 objects: { isOff: boolean, blocks: [{ start: String, end: String }] }
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  @@unique([userId, weekStart])
}

// DEPRECATED: Use UserSchedule instead
model TailorSchedule {
  id          Int      @id @default(autoincrement())
  tailorId    Int
  tailor      User     @relation("UserTailorSchedules", fields: [tailorId], references: [id])
  dayOfWeek   Int      // 0=Sunday, 6=Saturday
  startTime   String   // '09:00'
  endTime     String   // '17:00'
}

model Party {
  id                Int             @id @default(autoincrement())
  name              String?
  eventDate         DateTime
  customerId        Int
  customer          Customer        @relation(fields: [customerId], references: [id])
  salesPersonId     Int?
  salesPerson       User?           @relation("PartySalesPerson", fields: [salesPersonId], references: [id])
  suitStyle         String?
  suitColor         String?
  externalId        String?
  syncedAt          DateTime?
  alterationJobs    AlterationJob[]
  appointments      Appointment[]
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  notes             String?
  syncedToLs        Boolean         @default(false)
  lightspeedGroupId String?
  members           PartyMember[]
  notifications     NotificationHistory[] @relation("PartyNotifications")

  @@index([customerId])
  @@index([salesPersonId])
}

model AlterationJob {
  id                       Int                 @id @default(autoincrement())
  jobNumber                String              @unique // Auto-generated job number
  lightspeedServiceOrderId String?             @unique
  partyId                  Int?
  customerId               Int?
  partyMemberId            Int?
  partyMember              PartyMember?        @relation(fields: [partyMemberId], references: [id])
  notes                    String?
  status                   AlterationJobStatus @default(NOT_STARTED)
  orderStatus              OrderStatus         @default(ALTERATION_ONLY)
  timeSpentMinutes         Int?
  tailorId                 Int?
  measurements             Json?
  receivedDate             DateTime            @default(now())
  dueDate                  DateTime?
  rushOrder                Boolean             @default(false)
  createdAt                DateTime            @default(now())
  updatedAt                DateTime            @updatedAt
  party                    Party?              @relation(fields: [partyId], references: [id])
  customer                 Customer?           @relation(fields: [customerId], references: [id])
  tailor                   User?               @relation(fields: [tailorId], references: [id])
  jobParts                 AlterationJobPart[] @relation("JobParts")
  qrCode                   String?             @unique // Main job QR code
  workflowSteps            AlterationWorkflowStep[]
  assignmentLogs           AssignmentLog[]

  @@index([jobNumber])
  @@index([status])
  @@index([orderStatus])
  @@index([partyId])
  @@index([customerId])
  @@index([tailorId])
  @@index([receivedDate])
  @@index([dueDate])
  @@index([createdAt])
  @@index([rushOrder])
  @@index([lightspeedServiceOrderId])
  @@index([partyMemberId])
  @@index([qrCode])
  @@index([status, dueDate])
  @@index([customerId, status])
  @@index([tailorId, status])
}

enum AlterationJobStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETE
  PICKED_UP
  ON_HOLD
  pending
}

enum OrderStatus {
  ORDERED
  IN_STOCK
  ALTERATION_ONLY
}

model AlterationJobPart {
  id              Int                   @id @default(autoincrement())
  jobId           Int
  job             AlterationJob         @relation("JobParts", fields: [jobId], references: [id], onDelete: Cascade)
  partName        String                // e.g., "Jacket", "Pants", "Vest", "Shirt"
  partType        GarmentPartType
  status          AlterationJobStatus   @default(NOT_STARTED)
  assignedTo      Int?
  assignedUser    User?                 @relation("AssignedTailorJobParts", fields: [assignedTo], references: [id])
  abilityId       Int?
  ability         TailorAbility?        @relation(fields: [abilityId], references: [id])
  timeSpent       Int?                  // minutes
  qrCode          String                @unique // Unique QR code for this part
  notes           String?
  priority        PartPriority          @default(NORMAL)
  estimatedTime   Int?                  // estimated minutes
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt
  tasks           AlterationTask[]      @relation("PartTasks")
  scanLogs        QRScanLog[]           @relation("PartScanLogs")
  assignmentLogs  AssignmentLog[]


  @@index([jobId])
  @@index([assignedTo])
  @@index([qrCode])
  @@index([status])
}

enum GarmentPartType {
  JACKET
  PANTS
  VEST
  SHIRT
  DRESS
  SKIRT
  OTHER
}

enum PartPriority {
  LOW
  NORMAL
  HIGH
  RUSH
}

enum TaskType {
  ALTERATION
  BUTTON_WORK
  MEASUREMENT
  CUSTOM
}

model AlterationTask {
  id          Int                   @id @default(autoincrement())
  partId      Int
  part        AlterationJobPart     @relation("PartTasks", fields: [partId], references: [id], onDelete: Cascade)
  taskName    String                // e.g., "Hem", "Shorten Sleeves", "Take in Sides"
  taskType    TaskType              @default(ALTERATION)
  status      AlterationJobStatus   @default(NOT_STARTED)
  startTime   DateTime?
  finishTime  DateTime?
  assignedTo  Int?
  assignedUser User?                @relation("TaskAssignedUser", fields: [assignedTo], references: [id])
  timeSpent   Int?                  // minutes
  initials    String?               // Tailor initials/signature
  notes       String?
  measurements String?              // Specific measurements for this task
  createdAt   DateTime              @default(now())
  updatedAt   DateTime              @updatedAt
  taskLogs    AlterationTaskLog[]   @relation("TaskLogs")


  @@index([partId])
  @@index([assignedTo])
  @@index([status])
}

model AlterationTaskLog {
  id        Int      @id @default(autoincrement())
  taskId    Int
  task      AlterationTask @relation("TaskLogs", fields: [taskId], references: [id], onDelete: Cascade)
  userId    Int
  user      User     @relation("TaskLogUser", fields: [userId], references: [id])
  action    String   // "started", "finished", "paused", "updated"
  timestamp DateTime @default(now())
  notes     String?
  metadata  Json?    // Additional data like location, device, etc.

  @@index([taskId])
  @@index([userId])
  @@index([timestamp])
}

model AlterationWorkflowStep {
  id          Int           @id @default(autoincrement())
  jobId       Int
  job         AlterationJob @relation(fields: [jobId], references: [id], onDelete: Cascade)
  stepName    String        // e.g., "Measured", "Suit Ordered", "Alterations Marked"
  stepType    WorkflowStepType
  completed   Boolean       @default(false)
  completedAt DateTime?
  completedBy Int?
  completedByUser User?     @relation("WorkflowStepUser", fields: [completedBy], references: [id])
  notes       String?
  sortOrder   Int           @default(0)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@index([jobId])
  @@index([stepType])
}

enum WorkflowStepType {
  MEASURED
  SUIT_ORDERED
  SUIT_ARRIVED
  ALTERATIONS_MARKED
  COMPLETE
  QC_CHECKED
  READY_FOR_PICKUP
  PICKED_UP
}

model QRScanLog {
  id        Int               @id @default(autoincrement())
  qrCode    String
  partId    Int?
  part      AlterationJobPart? @relation("PartScanLogs", fields: [partId], references: [id])
  scannedBy Int
  user      User              @relation("QRScanUser", fields: [scannedBy], references: [id])
  scanType  QRScanType
  location  String?           // Optional location/device info
  timestamp DateTime          @default(now())
  metadata  Json?             // Additional scan data
  result    String?           // Success/error message

  @@index([qrCode])
  @@index([partId])
  @@index([scannedBy])
  @@index([timestamp])
}

enum QRScanType {
  START_WORK
  FINISH_WORK
  PICKUP
  STATUS_CHECK
  QUALITY_CHECK
}

model Appointment {
  id                 Int               @id @default(autoincrement())
  partyId            Int?
  party              Party?            @relation(fields: [partyId], references: [id])
  customerId         String?
  saleId             String?
  dateTime           DateTime
  durationMinutes    Int?
  type               AppointmentType?  @default(fitting)
  notes              String?
  status             AppointmentStatus @default(scheduled)
  syncedToLightspeed Boolean           @default(false)
  recurrenceRule     String?
  parentId           Int?
  parent             Appointment?      @relation("AppointmentRecurrence", fields: [parentId], references: [id])
  children           Appointment[]     @relation("AppointmentRecurrence")
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  memberId           Int?
  member             PartyMember?      @relation("MemberAppointments", fields: [memberId], references: [id])
  tailorId           Int?
  tailor             User?             @relation("TailorAppointments", fields: [tailorId], references: [id])
  lsEventId          String?
  // Workflow automation fields
  workflowStage      Int?              @default(1) // 1=first_fitting, 2=alterations_fitting, 3=pickup
  autoScheduleNext   Boolean           @default(false)
  nextAppointmentId  Int?
  nextAppointment    Appointment?      @relation("AppointmentSequence", fields: [nextAppointmentId], references: [id])
  previousAppointment Appointment[]    @relation("AppointmentSequence")
  // Individual customer support
  individualCustomerId Int?
  individualCustomer Customer?         @relation("IndividualCustomerAppointments", fields: [individualCustomerId], references: [id])
  // Notification tracking
  remindersScheduled Boolean           @default(false)
  remindersSent      Json?             // Track which reminders have been sent
  notificationSchedules NotificationSchedule[]
  notifications      NotificationHistory[] @relation("AppointmentNotifications")

  @@index([partyId])
  @@index([parentId])
  @@index([memberId])
  @@index([tailorId])
  @@index([dateTime])
  @@index([status])
  @@index([type])
  @@index([customerId])
  @@index([saleId])
  @@index([syncedToLightspeed])
  @@index([dateTime, status])
  @@index([partyId, dateTime])
  @@index([tailorId, dateTime])
}

enum AppointmentType {
  fitting
  first_fitting
  alterations_fitting
  pickup
  final_try
  other
}

enum AppointmentStatus {
  scheduled
  rescheduled
  canceled
  completed
  confirmed
  no_show
}

model SyncLog {
  id            Int           @id @default(autoincrement())
  createdAt     DateTime      @default(now())
  resource      String
  direction     SyncDirection
  status        String
  message       String?
  recordCount   Int
  lightspeedIds String[]
}

enum SyncDirection {
  inbound
  outbound
}

model SyncStatus {
  id                Int      @id @default(autoincrement())
  resource          String   @unique
  lastSyncedVersion BigInt?
  lastSyncedAt      DateTime @default(now()) @updatedAt
  status            String   @default("IDLE") // e.g., IDLE, SYNCING, SUCCESS, FAILED
  errorMessage      String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

model AuditLog {
  id        Int      @id @default(autoincrement())
  userId    Int?
  action    String // create, update, delete, sync
  entity    String // Customer, Party, etc.
  entityId  Int
  details   String // JSON string of change details
  createdAt DateTime @default(now())
  user      User?    @relation(fields: [userId], references: [id])

  @@index([entity, entityId])
  @@index([userId])
}

enum PartyMemberStatus {
  awaiting_measurements
  need_to_order
  ordered
  received
  being_altered
  ready_for_pickup
  Selected
}

model PartyMember {
  id             Int             @id @default(autoincrement())
  partyId        Int
  party          Party           @relation(fields: [partyId], references: [id])
  lsCustomerId   String? // Lightspeed customer ID
  role           String
  notes          String?
  status         PartyMemberStatus @default(awaiting_measurements)
  suitOrderId    String?
  accessoriesOrderId String?
  orderedAt      DateTime?
  receivedAt     DateTime?
  alteredAt      DateTime?
  readyForPickupAt DateTime?
  pickupDate      DateTime?      // Customer-selected pickup date (not time)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  appointments   Appointment[]   @relation("MemberAppointments")
  measurements   Json?
  alterationJobs AlterationJob[]
  weddingSuitId  Int?
  weddingSuit    WeddingSuit?    @relation(fields: [weddingSuitId], references: [id])
}

model SaleAssignment {
  id             Int      @id @default(autoincrement())
  saleId         String // Lightspeed sale ID
  associateId    Int
  associate      User     @relation("UserSaleAssignments", fields: [associateId], references: [id])
  commissionRate Float
  amount         Float
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model Skill {
  id    Int    @id @default(autoincrement())
  name  String @unique
  users User[]
}

model Settings {
  id                Int      @id @default(1)
  reminderIntervals String   @default("24,4")
  emailSubject      String   @default("Reminder: Your appointment at {shopName}")
  emailBody         String   @default("Hi {customerName},\nThis is a reminder for your appointment with {partyName} on {dateTime}.")
  smsBody           String   @default("Reminder: {partyName} appointment on {dateTime} at {shopName}.")
  // Enhanced notification settings
  earlyMorningCutoff String? // Time before which to send 1h instead of 3h reminder
  lateMorningCutoff  String?
  afternoonCutoff    String?
  pickupReadySubject String  @default("Your garment is ready for pickup!")
  pickupReadyEmail   String  @default("Hi {customerName},\nYour garment for {partyName} is ready for pickup!")
  pickupReadySms     String  @default("Your garment for {partyName} is ready for pickup at {shopName}!")
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

model NotificationSchedule {
  id            Int      @id @default(autoincrement())
  appointmentId Int
  appointment   Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  type          NotificationType
  scheduledFor  DateTime
  sent          Boolean  @default(false)
  sentAt        DateTime?
  method        NotificationMethod
  recipient     String   // email or phone number
  subject       String?
  message       String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([scheduledFor, sent])
  @@index([appointmentId])
}

enum NotificationType {
  appointment_reminder_24h
  appointment_reminder_3h
  appointment_reminder_1h
  pickup_ready
}

enum NotificationMethod {
  email
  sms
  both
}

model AlterationTaskType {
  id              Int             @id @default(autoincrement())
  name            String          @unique
  defaultDuration Int
  parts           String
  abilities       TailorAbility[]
}

model TailorAbility {
  id                 Int                 @id @default(autoincrement())
  tailorId           Int
  tailor             User                @relation("UserTailorAbilities", fields: [tailorId], references: [id])
  taskTypeId         Int
  taskType           AlterationTaskType  @relation(fields: [taskTypeId], references: [id])
  proficiency        Int // 1-5
  notes              String?
  alterationJobParts AlterationJobPart[]

  @@unique([tailorId, taskTypeId])
}

model PushSubscription {
  id       Int    @id @default(autoincrement())
  endpoint String @unique
  p256dh   String
  auth     String
  userId   Int?
}

model CommunicationLog {
  id        Int      @id @default(autoincrement())
  type      String // sms, email
  recipient String
  message   String
  status    String // sent, failed
  sentAt    DateTime @default(now())
}

// Comprehensive notification tracking and management
model NotificationHistory {
  id              Int      @id @default(autoincrement())
  type            String   // appointment_confirmation, appointment_reminder, party_invitation, etc.
  method          String   // email, sms, both
  recipient       String   // email or phone
  subject         String?
  message         String
  status          String   // sent, failed, pending
  sentAt          DateTime @default(now())
  deliveredAt     DateTime?
  failedAt        DateTime?
  errorMessage    String?
  
  // Related entities
  customerId      Int?
  customer        Customer? @relation("CustomerNotifications", fields: [customerId], references: [id])
  partyId         Int?
  party           Party? @relation("PartyNotifications", fields: [partyId], references: [id])
  appointmentId   Int?
  appointment     Appointment? @relation("AppointmentNotifications", fields: [appointmentId], references: [id])
  userId          Int?
  user            User? @relation("UserNotifications", fields: [userId], references: [id])
  
  // Template tracking
  templateId      Int?
  template        NotificationTemplate? @relation("TemplateNotifications", fields: [templateId], references: [id])
  
  // Metadata
  metadata        Json?    // Additional context like party name, wedding date, staff name, etc.
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([customerId])
  @@index([partyId])
  @@index([appointmentId])
  @@index([userId])
  @@index([type])
  @@index([status])
  @@index([sentAt])
  @@index([templateId])
}

model NotificationTemplate {
  id              Int      @id @default(autoincrement())
  name            String   @unique
  type            String   // appointment_confirmation, appointment_reminder, party_invitation, etc.
  subject         String?
  emailBody       String
  smsBody         String
  isDefault       Boolean  @default(false)
  isActive        Boolean  @default(true)
  description     String?
  variables       Json?    // Available placeholders like {customerName}, {partyName}, etc.
  createdBy       Int?
  createdByUser   User? @relation("TemplateCreator", fields: [createdBy], references: [id])
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Related notifications
  notifications   NotificationHistory[] @relation("TemplateNotifications")

  @@index([type])
  @@index([isDefault])
  @@index([isActive])
  @@index([createdBy])
}

// These are read-only mirrors of Lightspeed data for local querying
model Product {
  id                Int              @id @default(autoincrement())
  lightspeedId      String           @unique
  name              String
  sku               String?
  price             Float
  category          String?
  brand             String?
  lightspeedVersion BigInt?
  syncedAt          DateTime?
  saleLineItems     SaleLineItem[]

  @@index([lightspeedId])
}

model WeddingSuit {
  id          Int      @id @default(autoincrement())
  vendor      String   // e.g., "Tommy Hilfiger", "Calvin Klein"
  style       String   // e.g., "Classic Fit", "Slim Fit", "Modern Fit"
  color       String   // e.g., "Navy", "Charcoal", "Black"
  size        String   // e.g., "40R", "42L", "44S"
  price       Float?
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   Int?
  createdByUser User?  @relation("WeddingSuitCreator", fields: [createdBy], references: [id])

  // Relations
  partyMembers PartyMember[]

  @@index([vendor])
  @@index([style])
  @@index([color])
  @@index([size])
  @@index([isActive])
  @@index([createdBy])
}

model Sale {
  id                Int              @id @default(autoincrement())
  lightspeedId      String           @unique
  customerId        Int
  customer          Customer         @relation(fields: [customerId], references: [id])
  total             Float
  saleDate          DateTime
  lineItems         SaleLineItem[]
  syncedAt          DateTime?
}

model SaleLineItem {
  id           Int      @id @default(autoincrement())
  lightspeedId String   @unique
  saleId       Int
  sale         Sale     @relation(fields: [saleId], references: [id])
  productId    Int
  product      Product  @relation(fields: [productId], references: [id])
  quantity     Int
  price        Float
  syncedAt     DateTime @updatedAt
}

model SyncState {
  id          Int      @id @default(autoincrement())
  entity      String   @unique
  lastVersion BigInt
  lastSynced  DateTime @updatedAt
}

model ApiToken {
  id           Int      @id @default(autoincrement())
  service      String   @unique
  accessToken  String   @db.Text
  refreshToken String   @db.Text
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model AssignmentLog {
  id          Int      @id @default(autoincrement())
  jobId       Int
  partId      Int
  oldTailorId Int?
  newTailorId Int?
  userId      Int?
  method      String   // 'auto' or 'manual'
  reason      String?
  timestamp   DateTime @default(now())

  job         AlterationJob @relation(fields: [jobId], references: [id])
  part        AlterationJobPart @relation(fields: [partId], references: [id])
  user        User? @relation(fields: [userId], references: [id])

  @@index([jobId])
  @@index([partId])
  @@index([userId])
}

model UserSession {
  id                    String   @id @default(cuid())
  userId                Int      // Required link to local User
  browserSessionId      String   // Required for browser sessions
  lsAccessToken         String   @db.Text
  lsRefreshToken        String   @db.Text
  lsDomainPrefix        String
  expiresAt             DateTime
  lastActive            DateTime @default(now())
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  // Relations
  user                  User     @relation("UserSessions", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, browserSessionId])
  @@index([browserSessionId])
  @@index([userId])
  @@index([expiresAt])
  @@index([lastActive])
}

model Session {
  id      String   @id @default(cuid())
  sid     String   @unique
  data    String
  expires DateTime
}

// Checklists & Tasks System
model Checklist {
  id               Int                   @id @default(autoincrement())
  title            String
  description      String?
  frequency        ChecklistFrequency
  isRequired       Boolean               @default(false)
  estimatedMinutes Int?
  createdById      Int
  createdBy        User                  @relation("ChecklistCreator", fields: [createdById], references: [id])
  createdAt        DateTime              @default(now())
  updatedAt        DateTime              @updatedAt
  isActive         Boolean               @default(true)
  items            ChecklistItem[]
  assignments      ChecklistAssignment[]

  @@index([createdById])
  @@index([frequency])
  @@index([isActive])
}

model ChecklistItem {
  id          Int      @id @default(autoincrement())
  checklistId Int
  checklist   Checklist @relation(fields: [checklistId], references: [id], onDelete: Cascade)
  title       String
  description String?
  isRequired  Boolean  @default(false)
  order       Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  executions  ChecklistItemExecution[]

  @@index([checklistId])
  @@index([order])
}

model ChecklistAssignment {
  id           Int      @id @default(autoincrement())
  checklistId  Int
  checklist    Checklist @relation(fields: [checklistId], references: [id], onDelete: Cascade)
  assignedToId Int
  assignedTo   User     @relation("ChecklistAssignee", fields: [assignedToId], references: [id], onDelete: Cascade)
  assignedById Int
  assignedBy   User     @relation("ChecklistAssigner", fields: [assignedById], references: [id])
  dueDate      DateTime?
  assignedAt   DateTime @default(now())
  isActive     Boolean  @default(true)
  executions   ChecklistExecution[]

  @@index([assignedToId])
  @@index([checklistId])
  @@index([assignedById])
}

model ChecklistExecution {
  id           Int                      @id @default(autoincrement())
  assignmentId Int
  assignment   ChecklistAssignment      @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  userId       Int
  user         User                     @relation("ChecklistExecutor", fields: [userId], references: [id], onDelete: Cascade)
  status       ChecklistStatus          @default(NOT_STARTED)
  startedAt    DateTime?
  completedAt  DateTime?
  notes        String?
  scheduledFor DateTime
  createdAt    DateTime                 @default(now())
  updatedAt    DateTime                 @updatedAt
  itemExecutions ChecklistItemExecution[]

  @@index([userId, scheduledFor])
  @@index([assignmentId])
  @@index([status])
}

model ChecklistItemExecution {
  id          Int      @id @default(autoincrement())
  executionId Int
  execution   ChecklistExecution @relation(fields: [executionId], references: [id], onDelete: Cascade)
  itemId      Int
  item        ChecklistItem @relation(fields: [itemId], references: [id], onDelete: Cascade)
  isCompleted Boolean  @default(false)
  completedAt DateTime?
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([executionId, itemId])
  @@index([executionId])
  @@index([itemId])
}

model Task {
  id               Int          @id @default(autoincrement())
  title            String
  description      String?
  priority         TaskPriority @default(MEDIUM)
  status           TaskStatus   @default(PENDING)
  assignedToId     Int
  assignedTo       User         @relation("TaskAssignee", fields: [assignedToId], references: [id], onDelete: Cascade)
  assignedById     Int
  assignedBy       User         @relation("TaskAssigner", fields: [assignedById], references: [id])
  dueDate          DateTime?
  estimatedMinutes Int?
  completedAt      DateTime?
  notes            String?
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt

  @@index([assignedToId, status])
  @@index([assignedById])
  @@index([status])
  @@index([priority])
  @@index([dueDate])
}

enum ChecklistFrequency {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  OVERDUE
}

enum ChecklistStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  OVERDUE
}

model CustomerTag {
  id         Int       @id @default(autoincrement())
  name       String
  customers  Customer[] @relation("CustomerTags")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  @@unique([name])
}

model CustomerCustomField {
  id          Int      @id @default(autoincrement())
  customer    Customer @relation(fields: [customerId], references: [id])
  customerId  Int
  key         String
  value       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  @@unique([customerId, key])
}

model CustomerGroup {
  id         Int       @id @default(autoincrement())
  name       String
  externalId String?   // Lightspeed group ID
  customers  Customer[] @relation("CustomerGroups")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  @@unique([name, externalId])
}
