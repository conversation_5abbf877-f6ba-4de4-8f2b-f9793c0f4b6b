{"compilerOptions": {"target": "es2017", "module": "nodenext", "lib": ["ES2020"], "outDir": "dist", "rootDir": "src", "strict": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": false, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "isolatedModules": true}, "include": ["src"], "exclude": ["node_modules", "dist"]}