{"name": "suitsync-full", "version": "1.0.0", "private": true, "prisma": {"schema": "prisma/schema.prisma"}, "scripts": {"prisma:gen": "prisma generate", "tsoa:routes": "tsoa routes", "tsoa:spec": "tsoa spec", "build": "npm run prisma:gen && npm run tsoa:routes && npm run tsoa:spec && tsc", "seed": "node dist/seed.js", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "lint": "eslint src --ext .ts,.tsx --fix", "lint:check": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prisma:migrate": "prisma migrate deploy", "manual:sync-customers": "ts-node ./src/services/manualCustomerSync.ts"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@prisma/client": "^6.10.1", "@prisma/extension-accelerate": "^2.0.1", "@sendgrid/mail": "^8.1.5", "@sentry/node": "^9.30.0", "@tsoa/runtime": "^6.6.0", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.3", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "concurrently": "^8.2.2", "connect-redis": "^7.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "express-slow-down": "^2.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-cron": "^4.2.0", "node-schedule": "^2.1.1", "p-limit": "^6.2.0", "react-big-calendar": "^1.19.4", "recharts": "^3.0.2", "redis": "^4.6.7", "session-file-store": "^1.5.0", "supertest": "^7.1.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@types/connect-sqlite3": "^0.9.5", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/express-rate-limit": "^6.0.2", "@types/express-serve-static-core": "^4.19.6", "@types/express-session": "^1.18.2", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node-schedule": "^2.1.7", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.30.0", "jest": "^30.0.3", "prisma": "^6.10.1", "ts-jest": "^29.4.0", "ts-node-dev": "^2.0.0", "tsoa": "^6.6.0", "typescript": "^5.8.3", "ts-node": "^10.9.1"}}