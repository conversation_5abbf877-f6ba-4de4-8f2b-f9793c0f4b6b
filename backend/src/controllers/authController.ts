import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { withAccelerate } from '@prisma/extension-accelerate';
import logger from '../utils/logger';
import { PersistentUserSessionService } from '../services/persistentUserSessionService';

const prisma = new PrismaClient().$extends(withAccelerate());

// SuitSync uses Lightspeed OAuth only - no local login
export const login = async (req: Request, res: Response): Promise<void> => {
  res.status(400).json({
    error: 'Local login is not supported. Please use Lightspeed OAuth.',
    redirectTo: '/auth/start-lightspeed'
  });
};



export const logout = async (req: Request, res: Response): Promise<void> => {
  // Clear any JWT cookies (if they exist)
  res.clearCookie('token', { path: '/' });

  // Destroy the session (this clears Lightspeed tokens and user session)
  if (req.session) {
    await new Promise<void>(resolve => req.session.destroy(() => resolve()));
  }

  res.json({ message: 'Logged out successfully' });
};

export const clearSession = async (req: Request, res: Response): Promise<void> => {
  try {
    // Clear any JWT cookies
    res.clearCookie('token', { path: '/' });

    // Clear session data but keep the session ID
    if (req.session) {
      // Clear all session data
      Object.keys(req.session).forEach(key => {
        if (key !== 'id' && key !== 'cookie') {
          delete (req.session as any)[key];
        }
      });

      // Save the cleared session
      await new Promise<void>((resolve, reject) => {
        req.session.save((err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    }

    logger.info('Session cleared successfully', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.json({
      message: 'Session cleared successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error clearing session:', error);
    res.status(500).json({ error: 'Failed to clear session' });
  }
};

export const getSession = async (req: Request, res: Response): Promise<void> => {
  try {
    logger.info('[AuthController] getSession called');
    logger.info('[AuthController] Session keys:', req.session ? Object.keys(req.session) : 'No session');

    // Check for pure Lightspeed user authentication first
    if (req.session?.lightspeedUser) {
      const lightspeedUser = req.session.lightspeedUser;

      logger.info('[AuthController] Found lightspeedUser in session:', {
        id: lightspeedUser.id,
        name: lightspeedUser.name,
        email: lightspeedUser.email,
        role: lightspeedUser.role
      });

      // Include Lightspeed connection status
      const lightspeedStatus = {
        connected: !!req.session?.lsAccessToken,
        domain: req.session?.lsDomainPrefix || null,
        lastSync: req.session?.lastLightspeedSync || null
      };

      const sessionResponse = {
        id: lightspeedUser.id,
        name: lightspeedUser.name,
        email: lightspeedUser.email,
        role: lightspeedUser.role,
        photoUrl: lightspeedUser.photoUrl,
        lightspeedEmployeeId: lightspeedUser.lightspeedEmployeeId,
        isLightspeedUser: true,
        lightspeed: lightspeedStatus
      };

      logger.info('[AuthController] Returning lightspeedUser session response:', sessionResponse);
      res.json(sessionResponse);
      return;
    }

    // Check if we have tokens but no user data - try to restore from persistent session
    if (req.session?.lsAccessToken && req.session?.lsRefreshToken && req.session?.lsDomainPrefix && req.session?.userId) {
      logger.info('[AuthController] Found tokens but no user data, attempting to restore from persistent session');

      try {
        // Try to get user data from persistent session
        const persistentSession = await PersistentUserSessionService.getActiveSession(req.session.userId);

        if (persistentSession) {
          logger.info('[AuthController] Found persistent session, restoring user data');

          // Get user details from the User table
          const user = await prisma.user.findUnique({
            where: { id: persistentSession.userId }
          });

          if (user) {
            // Restore user data in current session
            req.session.lightspeedUser = {
              id: user.lightspeedEmployeeId || String(user.id),
              lightspeedId: user.lightspeedEmployeeId || String(user.id),
              email: user.email,
              name: user.name,
              role: user.role,
              lightspeedEmployeeId: user.lightspeedEmployeeId,
              photoUrl: user.photoUrl,
              hasLocalRecord: true,
              localUserId: user.id
            };

            // Save the session
            await new Promise<void>((resolve, reject) => {
              req.session.save((err) => {
                if (err) reject(err);
                else resolve();
              });
            });

            // Return the restored user data
            const lightspeedStatus = {
              connected: !!req.session?.lsAccessToken,
              domain: req.session?.lsDomainPrefix || null,
              lastSync: req.session?.lastLightspeedSync || null
            };

            const sessionResponse = {
              id: user.lightspeedEmployeeId || String(user.id),
              name: user.name,
              email: user.email,
              role: user.role,
              photoUrl: user.photoUrl,
              lightspeedEmployeeId: user.lightspeedEmployeeId,
              isLightspeedUser: true,
              lightspeed: lightspeedStatus
            };

            logger.info('[AuthController] Returning restored lightspeedUser session response:', sessionResponse);
            res.json(sessionResponse);
            return;
          }
        }
      } catch (error) {
        logger.error('[AuthController] Failed to restore user data from persistent session:', error);
      }
    }

    // If no session data at all, try to get user data from database based on URL parameters
    // This is a fallback for when the session is lost but we can identify the user
    if (!req.session?.lightspeedUser && !req.session?.lsAccessToken) {
      logger.info('[AuthController] No session data found, checking for URL-based user identification');
      
      // Check if we can identify the user from the request headers or referrer
      const referer = req.headers.referer;
      if (referer && referer.includes('auth=success') && referer.includes('user=')) {
        try {
          const url = new URL(referer);
          const userName = url.searchParams.get('user');
          
          if (userName) {
            logger.info(`[AuthController] Attempting to find user by name: ${userName}`);
            
            // Find user in database by name
            const user = await prisma.user.findFirst({
              where: { 
                name: { 
                  contains: decodeURIComponent(userName), 
                  mode: 'insensitive' 
                } 
              },
              select: {
                id: true,
                name: true,
                email: true,
                photoUrl: true,
                role: true,
                lightspeedEmployeeId: true
              }
            });
            
            if (user) {
              logger.info(`[AuthController] Found user in database: ${user.name}`);
              
              // Create a temporary session response (don't save to session since no tokens)
              const sessionResponse = {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role,
                photoUrl: user.photoUrl,
                lightspeedEmployeeId: user.lightspeedEmployeeId,
                isLightspeedUser: true,
                lightspeed: {
                  connected: false,
                  domain: null,
                  lastSync: null
                }
              };
              
              logger.info('[AuthController] Returning temporary user session response:', sessionResponse);
              res.json(sessionResponse);
              return;
            }
          }
        } catch (error) {
          logger.error('[AuthController] Error in URL-based user identification:', error);
        }
      }
    }

    // NO LEGACY AUTHENTICATION - Clear any old session data and require Lightspeed auth
    if (req.session && (req.session.activeUserId || req.session.userId || req.session.userSessions)) {
      logger.info('[AuthController] Found legacy session data, clearing and requiring re-authentication');

      // Clear legacy session data
      delete req.session.activeUserId;
      delete req.session.userId;
      delete req.session.userSessions;
      delete req.session.maxCachedUsers;

      // Save the cleared session
      await new Promise<void>((resolve, reject) => {
        req.session.save((err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    }

    // No valid Lightspeed session - require authentication
    logger.info('[AuthController] No lightspeedUser in session - requiring Lightspeed authentication');
    res.status(401).json({
      error: 'Lightspeed authentication required. Please sign in with Lightspeed.',
      errorCode: 'LS_AUTH_REQUIRED',
      redirectTo: '/auth/start-lightspeed'
    });
  } catch (error) {
    console.error('Error fetching user session data:', error);
    res.status(500).json({ error: 'Failed to fetch user data' });
  }
};

export const getUserPhoto = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email } = req.query;
    
    if (!email || typeof email !== 'string') {
      res.status(400).json({ error: 'Email parameter is required' });
      return;
    }

    // Find user by email and return their photo URL
    const user = await prisma.user.findFirst({
      where: { email: email.toLowerCase() },
      select: { 
        id: true, 
        name: true, 
        email: true, 
        photoUrl: true,
        role: true 
      }
    });

    if (!user) {
      res.status(404).json({ error: 'User not found' });
      return;
    }

    res.json({
      id: user.id,
      name: user.name,
      email: user.email,
      photoUrl: user.photoUrl,
      role: user.role
    });

  } catch (error) {
    logger.error('Error fetching user photo:', error);
    res.status(500).json({ error: 'Failed to fetch user photo' });
  }
};